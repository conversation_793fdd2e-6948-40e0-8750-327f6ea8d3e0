{"settings": {"title": "Configuración", "categories": {"personal": "Personal", "workspace": "Espacio de trabajo"}, "tabs": {"account": "C<PERSON><PERSON>", "security": "Seguridad", "notifications": "Notificaciones", "preferences": "Preferencias", "billing": "Facturación y planes", "team": "Equipo", "integrations": "Integraciones"}, "preferences": {"title": "Preferencias", "subheader": "Personaliza tu experiencia", "language": "Idioma", "languageLabel": "Seleccionar idioma", "languageSwitch": "{locale, select, en_US {English} pt_BR {Português} es {Español} other {Unknown}}", "theme": "<PERSON><PERSON>", "themeOptions": {"light": "<PERSON><PERSON><PERSON>", "dark": "Oscuro", "system": "Sistema"}, "preferences": {"title": "<PERSON><PERSON><PERSON> tus preferencias", "description": "Personaliza tu experiencia con BMS Pulse."}}, "notifications": {"title": "Notificaciones", "subheader": "Administrar las notificaciones", "email": "Correo electrónico", "phone": "Teléfono", "productUpdates": "Actualizaciones de productos", "securityUpdates": "Actualizaciones de seguridad", "saveChanges": "Guardar cambios"}, "password": {"title": "Contraseña", "subheader": "Actualizar contraseña", "currentPassword": "Contraseña actual", "newPassword": "Nueva contraseña", "confirmPassword": "Confirmar nueva contraseña", "update": "Actualizar", "currentPasswordRequired": "La contraseña actual es obligatoria", "newPasswordRequired": "La nueva contraseña es obligatoria", "confirmPasswordRequired": "Por favor, confirma tu nueva contraseña", "passwordMismatch": "Las contraseñas no coinciden", "minPasswordLength": "La contraseña debe tener al menos 6 caracteres", "successMessage": "Contraseña actualizada exitosamente", "errors": {"updateFailed": "Error al actualizar la contraseña", "wrongPassword": "La contraseña actual es incorrecta", "weakPassword": "La nueva contraseña es muy débil", "requiresRecentLogin": "Por favor, cierra sesión e inicia sesión nuevamente antes de cambiar tu contraseña"}}, "billing": {"title": "Facturación y Planes", "subheader": "Administrar tu suscripción y métodos de pago", "currentPlan": "Plan Actual", "availablePlans": "Planes Disponibles"}, "team": {"title": "Miembros del Equipo", "members": "Miembros del Equipo", "pendingInvitations": "Invitaciones Pendientes", "noPendingInvitations": "No hay invitaciones pendientes", "inviteMember": "Invitar <PERSON>", "name": "Nombre", "email": "Correo electrónico", "role": "Rol", "actions": "Acciones", "edit": "<PERSON><PERSON>"}, "integrations": {"title": "Integraciones", "loader": {"loadingText": "Cargando integraciones..."}, "tabs": {"all": "<PERSON><PERSON>", "installed": "Instaladas", "available": "No Instaladas"}, "updated": "Actualizado", "installs": "instalaciones", "search": "Buscar integración", "backToIntegrations": "Volver a Integraciones", "capabilitiesTitle": "Capacidades", "comingSoonMessage": "¡Esta integración estará disponible próximamente!", "notifyMessage": "Estamos trabajando arduamente para ofrecerte esta integración. ¿Te gustaría recibir una notificación cuando esté disponible?", "notifyButton": "Notificarme", "noConfigAvailable": "No hay configuración disponible para esta integración", "viewToggle": {"ariaLabel": "Modo de vista", "card": "Vista de tarjetas", "table": "Vista de tabla"}, "status": {"available": "Disponible", "comingSoon": "Próximamente", "installed": "Instalado"}, "table": {"name": "Nombre", "description": "Descripción", "status": "Estado", "actions": "Acciones"}, "actions": {"add": "<PERSON><PERSON><PERSON>", "import": "Importar", "export": "Exportar", "details": "Detalles", "install": "Instalar", "uninstall": "<PERSON><PERSON><PERSON><PERSON>", "uninstalling": "Desinstalando...", "save": "Guardar", "installing": "Instalando..."}, "github": {"settings": "Configuración de GitHub", "syncFrequency": "Frecuencia de sincronización", "syncFrequencyHelp": "Con qué frecuencia debemos sincronizar datos de tus repositorios de GitHub", "syncOptions": {"hourly": "<PERSON><PERSON> hora", "daily": "Diariamente", "weekly": "<PERSON><PERSON><PERSON><PERSON>"}, "webhookUrl": "URL del Webhook", "webhookUrlHelp": "Usa esta URL en la configuración de tu GitHub App para recibir eventos webhook", "dangerZone": "Zona de Peligro", "disconnectWarning": "Desconectar eliminará todos los datos de GitHub de tu cuenta. Esta acción no se puede deshacer.", "disconnect": "Desconectar GitHub", "installations": "Instalaciones de GitHub", "refreshIntegrations": "Actualizar integraciones", "noIntegrations": "No se encontraron organizaciones o usuarios de GitHub. Conecta tu cuenta de GitHub para comenzar.", "installationsInfo": "Las instalaciones de GitHub te permiten conectar tus organizaciones o cuentas personales de GitHub a BMS Pulse. Esto nos da acceso a las métricas de tus repositorios según los permisos necesarios para ofrecerte la mejor información.\n\nTodos los datos se gestionan de forma cuidadosa y segura, y solo tenemos acceso a lo que realmente necesitamos.", "installationDocs": "Aprende más sobre las instalaciones de Apps de GitHub", "account": "C<PERSON><PERSON>", "type": "Tipo", "status": "Estado", "connectedAt": "Conectado el", "actions": "Acciones", "organization": "Organización", "user": "Usuario", "statusActive": "Activo", "statusSuspended": "Suspendido", "statusUninstalled": "Desinstalado", "statusSynchronizing": "Sincronizando", "suspendEntity": "Suspender", "resumeEntity": "Reactivar", "uninstallEntity": "<PERSON><PERSON><PERSON><PERSON>", "editEntity": "<PERSON><PERSON>", "confirmUninstallEntity": "¿Estás seguro de que deseas desinstalar esta integración de GitHub? Esto eliminará todos los datos asociados.", "entitySuspended": "Integración de GitHub suspendida correctamente", "entityResumed": "Integración de GitHub reactivada correctamente", "entityUninstalled": "Integración de GitHub desinstalada correctamente", "errorSuspendingEntity": "Error al suspender la integración de GitHub", "errorReactivatingEntity": "Error al reactivar la integración de GitHub", "errorUninstallingEntity": "Error al desinstalar la integración de GitHub", "errorLoadingDetails": "Error al cargar los detalles de la integración de GitHub", "configSaved": "Configuración de GitHub guardada correctamente", "errorSavingConfig": "Error al guardar la configuración de GitHub", "saving": "Ahorro...", "addInstallation": "<PERSON><PERSON>dir <PERSON>", "installationAdded": "Instalación de GitHub añadida correctamente", "errorAddingInstallation": "Error al añadir la instalación de GitHub", "installedCount": "{count, plural, =0 {Sin instalaciones} =1 {1 instalación} other {# instalaciones}}", "action": {"title": "Instalación de GitHub", "subTitle": "Esta acción afectará la integración de GitHub con este espacio de trabajo.", "confirmationInstruction": "<PERSON> confirmar, escribe el nombre de la cuenta \"{accountName}\" a continuación:", "executedSuccessfully": "Instalación de GitHub ejecutada correctamente", "executedWithError": "Error al ejecutar la instalación de GitHub. Inténtalo de nuevo más tarde", "uninstalledDescription": "Esto eliminará la integración con esta cuenta de GitHub de este espacio de trabajo y detendrá la sincronización de datos.", "suspendedDescription": "Esto suspenderá la integración con esta cuenta de GitHub de este espacio de trabajo y detendrá la sincronización de datos.", "activeDescription": "Esto resumirá la integración con esta cuenta de GitHub de este espacio de trabajo y reiniciará la sincronización de datos.", "uninstalledWarning": "Si esta cuenta solo está conectada a Este espacio de trabajo también se desinstalará en GitHub. Si está vinculado a otros espacios de trabajo de BMS Pulse, permanecerá activo en GitHub.", "suspendedWarning": "Si esta cuenta solo está conectada a este espacio de trabajo, también se suspenderá en GitHub. Si está vinculada a otros espacios de trabajo de BMS Pulse, permanecerá activa en GitHub.", "activeWarning": "También se activará en GitHub si está actualmente suspendido. Otros espacios de trabajo vinculados con la misma cuenta de GitHub permanecerán suspendidos.", "accountLoginMismatch": "El nombre de la cuenta no coincide.", "cancelButton": "<PERSON><PERSON><PERSON>", "loading": "Cargando...", "resumeButton": "<PERSON><PERSON><PERSON>", "suspendButton": "Suspender", "uninstallButton": "<PERSON><PERSON><PERSON><PERSON>", "result": {"goToGitHubSettings": "Ir a la configuración de GitHub", "closeButton": "<PERSON><PERSON><PERSON>", "errorOnGitHub": "Se produjo un error en la comunicación con la API de GitHub.", "uninstallNotExecutedOnChannel": "La instalación se ha eliminado correctamente de este espacio de trabajo.\n\nSin embargo, dado que otros espacios de trabajo utilizan la misma cuenta de GitHub, permanece vinculada a BMS Pulse.", "uninstalledOnGitHubErrorDescription": "La instalación ya no está vinculada a este espacio de trabajo y ya no procesaremos datos para esta integración.\n\nSin embargo, tuvimos problemas al desinstalarla directamente de GitHub. Para eliminar completamente BMS Pulse de su cuenta, visite GitHub y confirme que la desinstalación se realizó correctamente.", "suspendNotExecutedOnChannel": "La instalación se ha suspendido correctamente en este espacio de trabajo.\n\nSin embargo, dado que otros espacios de trabajo utilizan la misma cuenta de GitHub, permanece activa en GitHub.", "suspendOnGitHubErrorDescription": "La instalación se ha suspendido en este espacio de trabajo y ya no procesaremos datos para esta integración.\n\nSin embargo, tuvimos problemas al suspenderla directamente en GitHub. Si también desea suspenderla o eliminarla de GitHub, visite GitHub directamente y verifique si la acción se realizó correctamente.", "withOtherWorkspaces": "Si desea que esta acción se refleje en GitHub y en todos los espacios de trabajo vinculados, deberá actualizar la instalación directamente en GitHub o realizar esta acción en cada espacio de trabajo asociado.\n\nTenga en cuenta que esto afectará a los siguientes espacios de trabajo:"}}}, "dangerZone": {"title": "Zona de Peligro", "description": "Las acciones en esta sección son irreversibles y afectarán permanentemente tu integración.", "uninstallTitle": "Desinstalar Integración", "uninstallDescription": "Esto eliminará permanentemente esta integración de su espacio de trabajo y todas las conexiones vinculadas a él.\nLos datos asociados no se eliminarán inmediatamente, pero podrían eliminarse después de un tiempo."}, "descriptions": {"github": "GitHub es un servicio de alojamiento web para el control de versiones de código usando Git.", "jira": "Jira es un producto de seguimiento de problemas desarrollado por Atlassian que permite el seguimiento de errores y la gestión de proyectos ágiles.", "slack": "Slack es una aplicación de mensajería para negocios que conecta a las personas con la información que necesitan.", "azuredevops": "Azure DevOps proporciona servicios de desarrollo para equipos de soporte para planificar el trabajo, colaborar en el desarrollo de código y construir y desplegar aplicaciones.", "bitbucket": "Bitbucket es un servicio de alojamiento de repositorios de código fuente basado en Git propiedad de Atlassian.", "gitlab": "GitLab es una herramienta de ciclo de vida DevOps basada en web que proporciona un gestor de repositorios Git con funciones de wiki, seguimiento de problemas y pipeline de CI/CD."}, "capabilities": {"pullRequests": "Análisis de pull/merge requests", "codeReview": "Métricas de revisión de código", "commit": "<PERSON><PERSON><PERSON><PERSON> de <PERSON>s", "repoInsights": "Insights de repositorio", "teamPerformance": "Seguimiento del rendimiento del equipo", "aiAnalytics": "Análisis de herramientas de IA", "issueTracking": "Integración de seguimiento de problemas", "sprintPerformance": "Métricas de rendimiento de sprint", "backlogAnalysis": "<PERSON><PERSON><PERSON><PERSON>log", "projectTimeline": "Insights de línea de tiempo del proyecto", "teamWorkload": "Distribución de carga de trabajo del equipo", "teamCommunication": "Análisis de comunicación del equipo", "channelActivity": "Métricas de actividad de canales", "notifications": "Integración de notificaciones", "alertDistribution": "Distribución de alertas", "collaborationInsights": "Insights de colaboración", "workItems": "Seguimiento de elementos de trabajo", "repositoryAnalytics": "Análisis de repositorio", "cicdPipelines": "Análisis de pipeline de CI/CD"}, "noResults": {"title": "No se encontraron integraciones", "description": "Ninguna integración coincide con tus criterios de búsqueda. Intenta ajustar los términos de búsqueda o navega por todas las integraciones disponibles.", "clearSearch": "Limpiar b<PERSON>"}, "errorState": {"title": "Error al cargar integraciones", "description": "No pudimos cargar las integraciones de tu workspace. Por favor verifica tu conexión e intenta nuevamente.", "retry": "Reintentar"}, "refresh": "Actualizar", "error": {"install": {"title": "Error Instalación de la integración", "description": "Se produjo un error al instalar la extensión. Inténtalo de nuevo más tarde."}}}, "account": {"title": "C<PERSON><PERSON>", "basicDetails": {"title": "Detalles básicos", "subheader": "Administra tu información personal", "displayName": "Nombre", "email": "Correo electrónico", "phone": "Número de teléfono", "countryLabel": "<PERSON><PERSON>", "timezone": "Zona horaria", "language": "Idioma", "saveChanges": "Guardar cambios", "countries": {"us": "Estados Unidos", "br": "Brasil", "gb": "Reino Unido", "ca": "Canadá", "jp": "Japón"}, "displayNameRequired": "Se requiere el nombre", "invalidEmail": "Correo electrónico no válido", "invalidPhone": "Número de teléfono no válido", "countryRequired": "<PERSON><PERSON>", "timezoneRequired": "La zona horaria no es válida", "maxNameSize": "El nombre debe ser menos de {size} caracteres", "avatarUploadError": "Error de carga de avatar", "noChanges": "No se realizaron cambios", "profileUpdatedSuccess": "Perfil actualizado exitosamente", "profileUpdateError": "Error al actualizar el perfil de usuario. Inténtalo más tarde"}, "profilePicture": {"remove": "Eliminar", "upload": "Subir", "uploadSuccess": "Foto de perfil actualizada exitosamente", "uploadError": "Error al subir la foto de perfil", "removeSuccess": "Foto de perfil eliminada exitosamente", "removeError": "Error al eliminar la foto de perfil", "fileTooLarge": "El tamaño del archivo debe ser menor a {size}MB", "invalidFileType": "Solo se permiten archivos PNG, JPEG y JPG", "confirmRemove": "¿Estás seguro de que quieres eliminar tu foto de perfil?", "cancel": "<PERSON><PERSON><PERSON>", "fileReadError": "Error al archivo de lectura"}, "deleteAccount": {"title": "Eliminar cuenta", "subheader": "Eliminar permanentemente tu cuenta y todo tu contenido", "description": "Esta acción no se puede deshacer. Una vez que elimines tu cuenta, todo tu contenido será eliminado permanentemente.", "button": "Eliminar cuenta"}}}, "git": {"overview": {"commitActivity": {"title": "Actividad de Commits", "today": "Hoy", "noActivity": "Sin actividad", "sync": "Sincronizar", "overview": "Resumen"}, "actions": {"viewAll": "Ver todo"}, "metrics": {"totalCommits": "Total de Commits", "pullRequests": "Pull Requests", "velocity": "Velocidad", "copilotMetrics": "Métricas de Copilot"}, "timeframes": {"sinceLastMonth": "Desde el mes pasado", "lastDays": "{days, plural, =1 {Último día} other {Últimos # días}}"}, "status": {"open": "<PERSON>bie<PERSON>o", "merged": "Fusionado", "closed": "<PERSON><PERSON><PERSON>", "unknown": "Desconocido"}, "tableHeaders": {"prNumber": "PR #", "author": "Autor", "date": "<PERSON><PERSON>", "status": "Estado"}, "copilot": {"suggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "acceptances": "Aceptaciones", "rejections": "Rechazos"}, "git": {"lastCommit": "Último commit: {date, date, medium} {date, time, short}"}, "titles": {"recentPullRequests": "Pull Requests Recientes", "recentRepositories": "Repositorios Recientes"}}, "repositories": {"title": "Repositories"}, "pullRequests": {"title": "Pull Requests"}, "commits": {"title": "Commits"}}, "customer": {"search": "Buscar cliente"}, "userPopup": {"account": "C<PERSON><PERSON>", "security": "Seguridad", "settings": "Configuraciones", "signOut": "<PERSON><PERSON><PERSON>", "noDisplayName": "Usuario"}, "insights": {"title": "Insights de Ingeniería", "description": "Métricas clave y respuestas a las preguntas más importantes para gerentes de ingeniería.", "categories": {"deliveryDeadlines": "Entrega y Plazos", "qualityRisk": "Calidad y Riesgo", "teamHealth": "Salud del Equipo", "processEfficiency": "Eficiencia de Proceso", "businessValue": "Valor de Negocio", "benchmarksComparison": "Benchmarks y Comparación", "costResources": "Costo y Recursos"}, "questions": {"sprintDeadline": "¿Cumpliremos la fecha del sprint / lanzamiento?", "blockingPRs": "¿Qué PRs están bloqueando el despliegue?", "pipelineBottleneck": "¿Qué equipo / módulo es el cuello de botella del pipeline?", "rework": "¿Cuánto retrabajo (churn) tuvimos esta semana?", "bugRate": "¿Está aumentando la tasa de errores después de las últimas fusiones?", "testCoverage": "¿Qué tan cerca estamos del objetivo de cobertura de pruebas?", "burnout": "¿Hay alguien al borde del agotamiento?", "workload": "¿Quién está sobrecargado y quién está subutilizado?", "reviewTime": "¿Cuánto tiempo pasamos solo esperando revisión?", "sprintImprovement": "¿Estamos mejorando de sprint a sprint?", "techDebtVsFeatures": "¿Cuánto esfuerzo se dedicó a funciones vs. deuda técnica?", "devROI": "¿Qué iniciativas aportan el mayor RO<PERSON> de desarrollo?", "marketComparison": "¿Estamos por encima o por debajo del percentil 50 del mercado para el tiempo de ciclo?", "internalBenchmark": "¿Qué equipo interno deberían imitar los demás?", "costPerEpic": "¿Cuál es el costo por épica por sprint y está dentro del presupuesto?", "cicdLostHours": "¿Cuántas horas de CI/CD se perdieron debido a fallos?"}, "metrics": {"burndownForecast": "Pronóstico de burndown", "percentDone": "% completado vs. planificado", "openPRs": "PRs abiertos > X h sin revisión", "cycleTime": "Tiempo de ciclo por equipo y etapa", "reworkPercentage": "% líneas reescritas ≤ 30 días", "incidentsPerKLOC": "Incidentes por KLOC / por lanzamiento", "coverageTarget": "Cobertura vs. objetivo", "consecutiveActiveHours": "Horas activas consecutivas", "offHoursCommits": "Commits fuera de horario", "workloadSpike": "Pico repentino de carga de trabajo", "wipPerDev": "WIP por desarrollador", "storyPointDistribution": "Distribución de story points", "reviewLeadTime": "Tiempo de espera de revisión", "prIdleTime": "Tiempo inactivo de PR", "deltaCycleTime": "Δ Tiempo de ciclo", "deltaLeadTime": "Δ Tiempo de espera", "deltaDeployFreq": "<PERSON> Frec. <PERSON> despliegue", "investmentProfile": "Perfil de inversión", "throughputVsOKR": "Throughput vs. impacto OKR", "anonymisedBenchmark": "Benchmark anonimizado", "internalHealthScore": "Clasificación de salud interna", "costPerStoryPoint": "Costo por story point", "buildFailMinutes": "Minutos de fallo de build", "pipelineMTTR": "MTTR del pipeline"}, "actions": {"rescope": "Redefinir alcance o añadir más personas", "notifyReviewers": "Notificar a revisores; asignar revisor automáticamente", "enablePairReview": "Habilitar revisión en pares o trabajo paralelo", "startRootCause": "Iniciar aná<PERSON>is de causa raíz / refactorización", "triggerQualityGate": "Activar puerta de calidad, ampliar pruebas", "createTestTasks": "<PERSON><PERSON><PERSON> tareas para pruebas críticas", "schedule1on1": "Programar 1-a-1, redistribuir tareas", "rebalanceBacklog": "Reequilibrar backlog", "prSizePolicy": "Política de tamaño de PR, rotaciones de revisores", "adjustCeremonies": "Ajustar ceremonias / límite WIP", "defendRefactoringTime": "Defender tiempo de refactorización", "prioritiseRoadmap": "Priorizar en el roadmap", "shareBestPractices": "Compartir mejores prácticas o solicitar coaching", "createGuilds": "<PERSON><PERSON><PERSON> gremios / dojos de aprendizaje", "reviewEstimates": "Revisar estimaciones o contratar", "automateRollback": "Automatizar rollback, optimizar pipeline"}}, "common": {"title": "BMS Pulse"}, "errors": {"notFound": {"title": "404: La página que buscas no está aquí", "description": "Intentaste una ruta sospechosa o llegaste aquí por error. Sea cual sea el caso, intenta usar la navegación", "action": "Volver al inicio", "altText": "En desarrollo"}}, "auth": {"guard": {"loadUserError": "Error al cargar los datos del usuario", "checkOnboardingError": "Error al verificar el estado de incorporación", "retryingIn": "Reintentando en {seconds} segundos...", "retryAttempt": "Intento de reintento: {attempt} (E<PERSON>ando {backoff} segundos)", "retryAttemptSimple": "Intento de reintento: {attempt}", "authError": "Error de autenticación", "notFoundError": "Recurso no encontrado", "networkError": "Error de conexión de red"}, "signIn": {"title": "In<PERSON><PERSON>", "emailLabel": "Correo electrónico", "emailRequired": "El correo electrónico es obligatorio", "passwordLabel": "Contraseña", "passwordRequired": "La contraseña es obligatoria", "submitButton": "In<PERSON><PERSON>", "noAccount": "¿No tienes una cuenta?", "signUpLink": "Regístrate", "forgotPassword": "¿Olvidaste tu contraseña?"}, "signUp": {"title": "Registrarse", "displayNameLabel": "Nombre", "displayNameRequired": "El nombre es obligatorio", "emailLabel": "Correo electrónico", "emailRequired": "El correo electrónico es obligatorio", "passwordLabel": "Contraseña", "passwordRequired": "La contraseña debe tener al menos 6 caracteres", "termsRequired": "Debes aceptar los términos y condiciones", "submitButton": "Registrarse", "haveAccount": "¿Ya tienes una cuenta?", "signInLink": "In<PERSON><PERSON>"}, "resetPassword": {"title": "Restablecer contraseña", "emailLabel": "Correo electrónico", "emailRequired": "El correo electrónico es obligatorio", "submitButton": "Enviar enlace de recuperación", "backToSignIn": "Volver a iniciar sesión", "rememberPassword": "¿Recuerdas tu contraseña?", "signInLink": "In<PERSON><PERSON>", "successMessage": "¡Correo de restablecimiento de contraseña enviado! Por favor, revisa tu bandeja de entrada y sigue las instrucciones."}, "errors": {"invalidCredentials": "Correo electrónico o contraseña inválidos", "authFailed": "Autenticación fallida. Por favor, inténtalo de nuevo.", "emailInUse": "Este correo electrónico ya está en uso. Por favor, usa un correo diferente o inicia sesión.", "weakPassword": "La contraseña es demasiado débil. Por favor, usa una contraseña más fuerte.", "registrationFailed": "Registro fallido. Por favor, verifica tu información e inténtalo de nuevo.", "resetFailed": "Error al enviar el correo de restablecimiento de contraseña", "userNotFound": "No se encontró ninguna cuenta con esta dirección de correo electrónico", "invalidEmail": "Formato de dirección de correo electrónico no válido", "tooManyRequests": "<PERSON><PERSON><PERSON><PERSON> solicitudes. Por favor, inténtalo de nuevo más tarde"}}, "nav": {"home": "<PERSON><PERSON>o", "insights": "Insights", "delivery": "Entrega y Plazos", "quality": "Calidad y Riesgo", "teamHealth": "Salud del Equipo", "process": "Eficiencia de Proceso", "business": "Valor de Negocio", "benchmarks": "Benchmarks", "cost": "Costo y Recursos", "customers": "Clientes", "integrations": "Integraciones", "settings": "Configuración", "account": "C<PERSON><PERSON>", "workspace": "Espacio de trabajo", "search": "Buscar", "contacts": "Contactos", "notifications": "Notificaciones", "analytics": "<PERSON><PERSON><PERSON><PERSON>", "git": "Git", "gitOverview": "Resumen", "repositories": "Repositorios", "pullRequests": "Pull Requests", "commits": "Commits"}, "layout": {"site": {"description": "Guía de startups para construir ecosistemas tecnológicos escalables y resistentes a través de estrategias procesables y experiencia en el mundo real."}, "welcome": "Bienvenido"}, "onboarding": {"steps": {"welcome": "Bienvenido", "userDetails": "<PERSON><PERSON>", "preferences": "Preferencias"}, "buttons": {"back": "Atrás", "next": "Siguient<PERSON>", "complete": "Completar", "saving": "Guardando...", "redirecting": "Redirigiendo...", "getStarted": "Comenzar", "logout": "<PERSON><PERSON><PERSON>"}, "errors": {"submitFailed": "Error al completar el proceso de incorporación. Por favor, inténtalo de nuevo.", "requiredFields": "Por favor, completa todos los campos obligatorios antes de continuar."}, "welcome": {"title": "¡Bienvenido a BMS Pulse!", "description": "¡Estamos emocionados de tenerte a bordo! Vamos a configurar tu cuenta para aprovechar al máximo BMS Pulse.", "imageAlt": "Ilustración de bienvenida"}, "userDetails": {"title": "Cuéntanos sobre ti", "description": "Esta información nos ayuda a personalizar tu experiencia.", "displayName": "Nombre", "email": "Correo electrónico", "phone": "Número de teléfono (opcional)", "invalidPhone": "Por favor, introduce un número de teléfono válido", "countryLabel": "<PERSON><PERSON>", "timezone": "Zona horaria", "displayNameRequired": "El nombre es obligatorio", "countries": {"us": "Estados Unidos", "br": "Brasil", "gb": "Reino Unido", "ca": "Canadá", "jp": "Japón"}, "countryRequired": "Se requiere país", "timezoneRequired": "Se requiere la zona horaria", "maxNameSize": "El nombre debe ser menos de {tamaño} caracteres"}, "preferences": {"title": "<PERSON><PERSON><PERSON> tus preferencias", "description": "Personaliza tu experiencia con BMS Pulse."}, "workspace": {"title": "Crea tu espacio de trabajo", "description": "Tu espacio de trabajo es donde administrarás tus proyectos y equipo.", "workspaceName": "Nombre del espacio de trabajo", "workspaceNamePlaceholder": "Mi Empresa", "workspaceNameRequired": "El nombre del espacio de trabajo es obligatorio", "workspaceNote": "<PERSON>uedes a<PERSON> mi<PERSON> del equipo y configurar tu espacio de trabajo más tarde."}, "completion": {"title": "¡Todo listo!", "description": "Tu cuenta ahora está lista para usar. A continuación, necesitarás crear o seleccionar un espacio de trabajo para comenzar.", "imageAlt": "Ilustración de configuración completada", "redirectMessage": "Redirigiendo a selección de espacio de trabajo en {seconds} segundos...", "processing": "Procesando...", "redirecting": "Redirigiendo a selección de espacio de trabajo..."}}, "workspace": {"selection": {"title": "Selecciona un espacio de trabajo", "description": "Elige un espacio de trabajo para continuar o crea uno nuevo.", "noWorkspaces": "No se encontraron espacios de trabajo", "noWorkspacesDescription": "Aún no tienes acceso a ningún espacio de trabajo. Crea tu primer espacio de trabajo para comenzar.", "createWorkspace": "<PERSON><PERSON>r espacio de trabajo", "selectWorkspace": "Seleccionar espacio de trabajo", "workspaceName": "Nombre del espacio de trabajo", "workspaceNamePlaceholder": "Mi Empresa", "workspaceNameRequired": "El nombre del espacio de trabajo es obligatorio", "createButton": "<PERSON><PERSON><PERSON>", "cancelButton": "<PERSON><PERSON><PERSON>", "creating": "Creando...", "selecting": "Seleccionando...", "createWorkspaceTitle": "Crear nuevo espacio de trabajo", "createWorkspaceDescription": "Ingresa un nombre para tu nuevo espacio de trabajo.", "loadingWorkspaces": "Cargando espacios de trabajo...", "searchWorkspaces": "Buscar espacios de trabajo...", "noSearchResults": "No se encontraron espacios de trabajo", "noSearchResultsDescription": "Intenta ajustar tus términos de búsqueda o crea un nuevo espacio de trabajo.", "editWorkspace": "Editar Espacio de Trabajo", "deleteWorkspace": "Eliminar Espacio de Trabajo", "editWorkspaceTitle": "Editar Espacio de Trabajo", "editWorkspaceDescription": "Actualiza los detalles de tu espacio de trabajo", "deleteWorkspaceTitle": "Eliminar Espacio de Trabajo", "deleteWorkspaceDescription": "Esta acción no se puede deshacer", "workspaceAvatar": "Avatar del Espacio de Trabajo", "uploadAvatarDescription": "Haz clic para subir un avatar para tu espacio de trabajo", "avatarRequirements": "PNG, JPG hasta 2MB", "changeAvatar": "Cambiar <PERSON>", "saveChanges": "Guardar Cambios", "saving": "Guardando...", "deleting": "Eliminando...", "deleteConfirmation": "¿Estás seguro de que quieres eliminar", "deleteWarning": "Todos los datos asociados con este espacio de trabajo serán eliminados permanentemente.", "deleteButton": "Eliminar", "createWorkspaceCardDescription": "Crea un nuevo espacio de trabajo para comenzar", "errors": {"createFailed": "Error al crear el espacio de trabajo. Inténtalo de nuevo.", "selectFailed": "Error al seleccionar el espacio de trabajo. Inténtalo de nuevo.", "loadFailed": "Error al cargar los espacios de trabajo. Inténtalo de nuevo.", "editFailed": "Error al actualizar el espacio de trabajo. Inténtalo de nuevo.", "deleteFailed": "Error al eliminar el espacio de trabajo. Inténtalo de nuevo."}}}, "home": {"onboarding": {"welcome": {"title": "Bienvenido a BMS Pulse", "subtitle": "Tu plataforma de análisis de ingeniería para ayudarte a tomar decisiones basadas en datos y mejorar tu proceso de desarrollo."}, "getStarted": {"title": "Comienza con BMS Pulse", "subtitle": "Completa estos pasos para configurar tu espacio de trabajo y comenzar a obtener información"}, "steps": {"gitIntegration": {"title": "Conectar Repositorio Git", "description": "Conecta tus repositorios de GitHub, GitLab o Bitbucket para comenzar a rastrear tus métricas de desarrollo."}, "exploreInsights": {"title": "Explorar Insights", "description": "Descubre información procesable sobre tu proceso de desarrollo, rendimiento del equipo y calidad del código."}, "customizeSettings": {"title": "Personaliza tu Experiencia", "description": "Configura tus preferencias, notificaciones y miembros del equipo para adaptar BMS Pulse a tus necesidades."}}, "quickStart": {"title": "Guía de Inicio Rápido", "subtitle": "Salta directamente y explora la plataforma", "description": "BMS Pulse proporciona análisis e información en tiempo real para tu equipo de desarrollo. Comienza explorando el panel de Control de Git para ver la actividad de tu equipo de un vistazo.", "button": "<PERSON><PERSON> <PERSON>", "imageAlt": "Vista previa del panel"}, "common": {"getStarted": "Comenzar", "learnMore": "Saber <PERSON>", "skip": "<PERSON><PERSON><PERSON> por ahora"}}}, "landing": {"header": {"termsOfService": "Términos de servicio", "privacyPolicy": "Política de privacidad", "bookDemo": "Reservar demostración", "features": "Características", "pricing": "<PERSON><PERSON><PERSON>", "integrations": "Integraciones"}, "badge": {"backedBy": "Respaldado por", "yCombinator": "Y Combinator", "trustedBy": ""}, "hero": {"title": "Claridad de Ingeniería en", "titleHighlight": "Un Pulse", "subtitle": "Agentes de IA personalizan métricas e insights predictivos para CTOs y gerentes de ingeniería, transformando datos de desarrollo en decisiones estratégicas.", "bookDemo": "Reservar Demostración Ejecutiva", "signIn": "<PERSON><PERSON><PERSON>", "enterApp": "Acceder al Dashboard", "ctaSecondary": "Unirse a la lista de espera", "metrics": {"teams": "", "insights": "", "roi": ""}}, "features": {"title": "Inteligencia de Ingeniería Impulsada por IA", "subtitle": "Diseñado específicamente para la toma de decisiones ejecutivas y planificación estratégica", "aiAgents": {"title": "Sistema Multi-Agente de IA", "description": "Agentes de IA especializados para diferentes personas - CTOs obtienen insights estratégicos, Gerentes de Ingeniería obtienen métricas operacionales"}, "integrations": {"title": "Ecosistema de Integración Empresarial", "description": "Integración perfecta con <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> y más de 20 herramientas de desarrollo"}, "insights": {"title": "Análisis Predictivo y Pronósticos", "description": "Algoritmos avanzados predicen cronogramas de entrega, identifican cuellos de botella y recomiendan asignación de recursos"}, "security": {"title": "Seguridad de Nivel Empresarial", "description": "Compatible con SOC 2 con cifrado de extremo a extremo y controles de acceso basados en roles"}}, "socialProof": {"title": "", "subtitle": "Experimenta el futuro del análisis de ingeniería con BMS Pulse", "testimonials": {"cto": {"quote": "", "name": "", "title": ""}, "vp": {"quote": "", "name": "", "title": ""}}}, "integrations": {"title": "Ecosistema de Integración Perfecta", "subtitle": "Conéctate con más de 20 herramientas y plataformas de desarrollo. Configuración con un clic, sincronización de datos en tiempo real y seguridad de nivel empresarial.", "categories": {"development": {"title": "Desarrollo y Control de Versiones", "description": "Conecta tu flujo de trabajo de desarrollo para insights integrales de código"}, "projectManagement": {"title": "Gestión de Proyectos y Planificación", "description": "Conecta tus herramientas de gestión de proyectos para insights integrales de entrega"}, "communication": {"title": "Comunicación y Colaboración", "description": "Rastrea patrones de comunicación del equipo y efectividad de la colaboración"}}, "tools": {"slack": {"description": "Notificaciones automatizadas e insights de colaboración del equipo", "features": ["Alertas inteligentes", "Reportes personalizados"]}, "teams": {"description": "Comunicación empresarial e insights de reuniones", "features": ["An<PERSON><PERSON><PERSON> de reuniones", "Insights del equipo"]}, "discord": {"description": "Comunidad de desarrolladores y seguimiento de colaboración", "features": ["Insights de la comunidad", "Seguimiento de actividad"]}}, "benefits": {"title": "Beneficios de Integración de Nivel Empresarial", "oneClick": {"title": "Configuración con Un Clic", "description": "Conecta todas tus herramientas en minutos con nuestro proceso de configuración automatizada"}, "security": {"title": "Seguridad Empresarial", "description": "Compatible con SOC 2 con cifrado de extremo a extremo y controles de acceso basados en roles"}, "realTime": {"title": "Sincronización en Tiempo Real", "description": "Sincronización de datos en vivo en todas las plataformas para insights instantáneos"}}}, "mockup": {"projectTitle": "⚠️ Crítico: Velocidad del sprint bajó 25%", "projectSubtitle": "IA detectó cuello de botella en el proceso de revisión de código - acción inmediata requerida", "analyzingStack": "<PERSON><PERSON><PERSON><PERSON> stack"}, "languageSwitch": "{locale, select, en_US {English (US)} pt_BR {Português (BR)} es {Español} other {Desconocido}}", "pricing": {"title": "Planes de Precios Ejecutivos", "subtitle": "Soluciones escalables diseñadas para organizaciones de ingeniería en crecimiento", "mostPopular": "Más Popular", "buttons": {"getStarted": "Comenzar", "startTrial": "Iniciar Prueba Ejecutiva", "contactSales": "<PERSON><PERSON>"}, "cto": {"title": "Suite Ejecutiva", "price": "<PERSON><PERSON>", "period": "precio personalizado", "description": "Para CTOs y VP de Ingeniería", "features": ["Dashboard estratégico y KPIs", "Suite de reportes ejecutivos", "Análisis predictivo", "Insights multi-equipo", "Soporte prioritario"]}, "manager": {"title": "<PERSON><PERSON><PERSON>", "price": "<PERSON><PERSON>", "period": "precio personalizado", "description": "Para Gerentes de Ingeniería", "features": ["Métricas de rendimiento del equipo", "<PERSON><PERSON><PERSON><PERSON>", "Productividad del desarrollador", "Gestión de integración", "Soporte estándar"]}, "enterprise": {"title": "Empresarial", "price": "<PERSON><PERSON>", "period": "precio personalizado", "description": "Para grandes organizaciones", "features": ["Agentes de IA personalizados", "Solución de marca blanca", "Gerente de éxito dedicado", "Integraciones personalizadas", "Soporte premium 24/7"]}}, "footer": {"copyright": "© 2025 BMS Pulse. Todos los derechos reservados.", "tagline": "Transforma tu análisis de desarrollo"}}}